# cocos-games 游戏项目

这是一个基于 Cocos Creator 3.8.5 的多游戏 Monorepo 项目，包含多个独立的游戏包。

## 项目结构

```
bomb-duck/
├── packages/                    # 游戏包目录
│   ├── game-shark/             # 鲨鱼游戏
│   ├── game-dixit/             # Dixit 游戏
│   ├── game-pirate/            # 海盗游戏
│   ├── game-flyingchess/       # 飞行棋游戏
│   ├── shared/                 # 共享资源包
│   ├── cli/                    # 命令行工具
│   ├── mobx-tsuki/             # MobX 状态管理库
│   ├── pitayaclient/           # Pitaya 客户端库
│   └── sgc/                    # SGC 库
├── docs/                       # 项目文档
├── scripts/                    # 构建脚本
└── README.md                   # 项目说明
```

## 版本管理

请查看 [Monorepo 版本管理快速开始](./docs/版本管理快速开始.md)

## 开发环境配置

请查看 [开发环境配置](./docs/开发环境配置.md)

## CLI 工具

项目提供了命令行工具用于项目创建、升级和管理：

-   **CLI 工具文档**: [CLI 工具使用指南](./packages/cli/readme.md)

## 游戏列表

### 🦈 Game Shark (鲨鱼游戏)

一个基于 Cocos Creator 的 3D 卡牌游戏。

-   **更新日志**: [CHANGELOG](./packages/game-shark/CHANGELOG.md)
-   **Bug 记录**: [Bugs](./packages/game-shark/Bugs.md)

### 🎨 Game Dixit

一个创意联想卡牌游戏。

-   **更新日志**: [CHANGELOG](./packages/game-dixit/CHANGELOG.md)
-   **技术分享**: [Cocos 游戏开发分享](./packages/game-dixit/docs/cocos-game-development-sharing.md)

### 🏴‍☠️ Game Pirate (海盗游戏)

一个海盗主题的冒险游戏。

-   **更新日志**: [CHANGELOG](./packages/game-pirate/CHANGELOG.md)

### ✈️ Game Flying Chess (飞行棋游戏)

经典飞行棋游戏的数字化版本。

-   **开发中** - 暂无文档
